import { Marker, Popup } from 'react-leaflet';
import L from 'leaflet';

/**
 * Create a custom marker icon with rank number
 */
const createRankIcon = (rank, isSelected = false) => {
  const color = getRankColor(rank);
  const size = isSelected ? 40 : 35;
  
  return L.divIcon({
    className: 'custom-rank-marker',
    html: `
      <div class="rank-marker ${isSelected ? 'selected' : ''}" style="
        background-color: ${color};
        width: ${size}px;
        height: ${size}px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: ${size > 35 ? '14px' : '12px'};
        border: 3px solid white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        position: relative;
      ">
        ${rank > 100 ? '100+' : rank}
      </div>
    `,
    iconSize: [size, size],
    iconAnchor: [size / 2, size / 2],
    popupAnchor: [0, -size / 2]
  });
};

/**
 * Get color based on ranking position
 */
const getRankColor = (rank) => {
  if (rank <= 3) return '#28a745'; // Green for top 3
  if (rank <= 10) return '#ffc107'; // Yellow for top 10
  if (rank <= 20) return '#fd7e14'; // Orange for top 20
  if (rank <= 50) return '#dc3545'; // Red for top 50
  return '#6c757d'; // Gray for 50+
};

/**
 * Get average rank for multiple keywords
 */
const getAverageRank = (rankingData) => {
  console.log('Calculating average rank for:', rankingData);
  if (rankingData.length === 0) return 0;
  const validRanks = rankingData.filter(data => data.position > 0);
  console.log('Valid ranks:', validRanks);
  if (validRanks.length === 0) return 0;

  const sum = validRanks.reduce((acc, data) => acc + data.position, 0);
  const average = Math.round(sum / validRanks.length);
  console.log('Calculated average:', average);
  return average;
};

/**
 * Get best rank for multiple keywords
 */
const getBestRank = (rankingData) => {
  if (rankingData.length === 0) return 0;
  const validRanks = rankingData.filter(data => data.position > 0);
  if (validRanks.length === 0) return 0;
  
  return Math.min(...validRanks.map(data => data.position));
};

const RankingMarker = ({ 
  position, 
  rankingData, 
  projectName, 
  locationName, 
  selectedKeyword,
  selectedDate,
  showBestRank = false 
}) => {
  // Determine which rank to show
  let displayRank;
  let rankLabel;
  
  if (selectedKeyword !== 'all') {
    // Show rank for specific keyword
    const keywordData = rankingData.find(data => data.keyword === selectedKeyword);
    displayRank = keywordData ? keywordData.position : 0;
    rankLabel = `${selectedKeyword}: Position ${displayRank}`;
  } else {
    // Show average or best rank for all keywords
    if (showBestRank) {
      displayRank = getBestRank(rankingData);
      rankLabel = `Best Position: ${displayRank}`;
    } else {
      displayRank = getAverageRank(rankingData);
      rankLabel = `Average Position: ${displayRank}`;
    }
  }

  // Don't render if no valid rank (but allow positions > 100)
  if (!displayRank || displayRank <= 0) {
    console.log('No valid rank for marker:', { displayRank, rankingData, selectedKeyword });
    // Temporarily show a default marker for debugging
    displayRank = 999;
    rankLabel = 'No Ranking Data';
  }

  const icon = createRankIcon(displayRank, selectedKeyword !== 'all');

  return (
    <Marker position={position} icon={icon}>
      <Popup>
        <div className="popup-content">
          <h3>{projectName}</h3>
          <p><strong>Location:</strong> {locationName}</p>
          <p><strong>{rankLabel}</strong></p>
          
          {selectedDate && (
            <p><strong>Date:</strong> {new Date(selectedDate).toLocaleDateString()}</p>
          )}
          
          {rankingData.length > 0 && (
            <div className="ranking-info">
              <h4>
                {selectedKeyword === 'all' ? 'All Keywords:' : 'Keyword Details:'}
              </h4>
              {rankingData
                .filter(data => selectedKeyword === 'all' || data.keyword === selectedKeyword)
                .map((data, index) => (
                  <div key={index} className="keyword-ranking">
                    <strong>{data.keyword}:</strong> Position {data.position}
                    {data.bestPosition && data.bestPosition !== data.position && (
                      <span className="best-position"> (Best: {data.bestPosition})</span>
                    )}
                  </div>
                ))}
            </div>
          )}
          
          <div className="rank-legend">
            <h5>Rank Colors:</h5>
            <div className="legend-items">
              <span className="legend-item">
                <span className="legend-color" style={{backgroundColor: '#28a745'}}></span>
                Top 3
              </span>
              <span className="legend-item">
                <span className="legend-color" style={{backgroundColor: '#ffc107'}}></span>
                Top 10
              </span>
              <span className="legend-item">
                <span className="legend-color" style={{backgroundColor: '#fd7e14'}}></span>
                Top 20
              </span>
              <span className="legend-item">
                <span className="legend-color" style={{backgroundColor: '#dc3545'}}></span>
                Top 50
              </span>
              <span className="legend-item">
                <span className="legend-color" style={{backgroundColor: '#6c757d'}}></span>
                50+
              </span>
            </div>
          </div>
        </div>
      </Popup>
    </Marker>
  );
};

export default RankingMarker;
