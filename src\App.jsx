import { useState } from 'react';
import './App.css';

function App() {
  const [count, setCount] = useState(0);

  console.log('App component rendering...');

  return (
    <div style={{ padding: '2rem', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: '#333' }}>SerpRobot Rankings Map</h1>
      <p style={{ color: '#666' }}>Test page - React is working!</p>
      <div style={{ background: '#f0f0f0', padding: '1rem', borderRadius: '4px', marginTop: '1rem' }}>
        <p><strong>Status:</strong> Application loaded successfully</p>
        <p><strong>Time:</strong> {new Date().toLocaleString()}</p>
        <p><strong>Counter:</strong> {count}</p>
        <button
          onClick={() => setCount(count + 1)}
          style={{
            padding: '0.5rem 1rem',
            background: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Click me! ({count})
        </button>
      </div>
    </div>
  );
}

export default App;
